// Test script to verify expected characteristics are being returned in getUserTastingHistory
import { createStorage } from './server/storage.ts';

async function testExpectedCharacteristics() {
  console.log('Testing expected characteristics in getUserTastingHistory...');
  
  try {
    const storage = createStorage();
    
    // Test with a known email (you can replace this with an actual test email)
    const testEmail = '<EMAIL>';
    
    console.log(`\nFetching tasting history for: ${testEmail}`);
    
    const result = await storage.getUserTastingHistory(testEmail, { limit: 10, offset: 0 });
    
    console.log('\n=== TASTING HISTORY RESULT ===');
    console.log(`Total sessions: ${result.total}`);
    console.log(`History entries: ${result.history.length}`);
    
    if (result.history.length > 0) {
      result.history.forEach((session, index) => {
        console.log(`\n--- Session ${index + 1} ---`);
        console.log(`Package: ${session.packageName}`);
        console.log(`Session ID: ${session.sessionId}`);
        console.log(`Wines count: ${session.winesTasted}`);
        
        if (session.wines && session.wines.length > 0) {
          console.log(`\nWines in package:`);
          session.wines.forEach((wine, wineIndex) => {
            console.log(`  ${wineIndex + 1}. ${wine.name} (${wine.type})`);
            if (wine.expectedCharacteristics) {
              console.log(`     Expected characteristics:`, wine.expectedCharacteristics);
            }
          });
        }
        
        if (session.expectedCharacteristics && session.expectedCharacteristics.length > 0) {
          console.log(`\nExpected characteristics for chips:`);
          session.expectedCharacteristics.forEach((char, charIndex) => {
            console.log(`  ${charIndex + 1}. ${char.category}: ${char.value} (from ${char.wineName})`);
          });
        } else {
          console.log('\nNo expected characteristics found for this session');
        }
      });
    } else {
      console.log('\nNo tasting history found for this user');
    }
    
  } catch (error) {
    console.error('Error testing expected characteristics:', error);
  }
}

// Run the test
testExpectedCharacteristics();
