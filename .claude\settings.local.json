{"permissions": {"allow": ["WebFetch(domain:docs.anthropic.com)", "Bash(find:*)", "Bash(grep:*)", "Bash(node test-templates.js)", "Bash(rg:*)", "Bash(npx drizzle-kit:*)", "<PERSON><PERSON>(echo:*)", "Bash(psql:*)", "<PERSON><PERSON>(cat:*)", "Bash(npm run dev:*)", "Bash(npm start)", "Bash(npm run build:*)", "Bash(npm run db:push:*)", "Bash(npm run check:*)", "Bash(npm install:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(kill:*)", "Bash(node:*)", "Bash(npx tsx:*)", "<PERSON><PERSON>(curl:*)", "Bash(rm:*)", "Bash(ls:*)", "Bash(# Delete existing question slides from the other 3 wines\npsql $DATABASE_URL -c \"DELETE FROM slides WHERE package_wine_id IN ('8515e452-d837-44ea-803c-9dc1704736ff', 'bf8eac9b-01a6-48f9-99da-ae74173c77aa', 'f4bf7d87-57ad-4ae3-a07f-97ba327634ef') AND type = 'question';\")", "<PERSON><PERSON>(chmod:*)", "Bash(./add_missing_slides.sh:*)", "Bash(npm config:*)", "Bash(/home/<USER>/workspace/.config/npm/node_global/bin/claude-code settings set autoUpdate true)", "Bash(npm run:*)", "Bash(awk:*)", "Bash(tree:*)", "Bash(git checkout:*)", "mcp__supabase__execute_sql", "mcp__supabase__get_logs", "mcp__supabase__apply_migration", "<PERSON><PERSON>(mv:*)", "mcp__supabase__list_tables", "Bash(PORT=5173 npm run dev)", "Bash(npx tsc:*)", "<PERSON><PERSON>(timeout 10s npm start)", "Bash(madge:*)", "<PERSON><PERSON>(sed:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git config:*)", "Bash(git push:*)", "mcp__supabase__search_docs", "Bash(timeout 10 bash -c 'echo \"no\" | npm run db:push' 2>&1)", "Bash(ss:*)"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["supabase"]}