// Test the expected characteristics API endpoint
async function testExpectedCharacteristics() {
  console.log('Testing expected characteristics API...');
  
  // Test with one of the known emails from the switch script
  const testEmail = '<EMAIL>';
  const apiUrl = `http://localhost:5000/api/dashboard/${testEmail}/history?limit=5&offset=0`;
  
  try {
    console.log(`\nFetching tasting history from: ${apiUrl}`);
    
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    
    console.log('\n=== API RESPONSE ===');
    console.log(`Total sessions: ${data.total}`);
    console.log(`History entries: ${data.history.length}`);
    
    if (data.history.length > 0) {
      data.history.forEach((session, index) => {
        console.log(`\n--- Session ${index + 1} ---`);
        console.log(`Package: ${session.packageName}`);
        console.log(`Session ID: ${session.sessionId}`);
        console.log(`Wines count: ${session.winesTasted}`);
        
        if (session.wines && session.wines.length > 0) {
          console.log(`\nWines in package:`);
          session.wines.forEach((wine, wineIndex) => {
            console.log(`  ${wineIndex + 1}. ${wine.name} (${wine.type})`);
            if (wine.expectedCharacteristics) {
              console.log(`     Expected characteristics:`, wine.expectedCharacteristics);
            }
          });
        }
        
        if (session.expectedCharacteristics && session.expectedCharacteristics.length > 0) {
          console.log(`\nExpected characteristics for chips (${session.expectedCharacteristics.length} total):`);
          session.expectedCharacteristics.slice(0, 10).forEach((char, charIndex) => {
            console.log(`  ${charIndex + 1}. ${char.category}: ${char.value} (from ${char.wineName})`);
          });
          if (session.expectedCharacteristics.length > 10) {
            console.log(`  ... and ${session.expectedCharacteristics.length - 10} more`);
          }
        } else {
          console.log('\nNo expected characteristics found for this session');
        }
      });
    } else {
      console.log('\nNo tasting history found for this user');
    }
    
  } catch (error) {
    console.error('Error testing expected characteristics:', error);
  }
}

// Run the test
testExpectedCharacteristics();
