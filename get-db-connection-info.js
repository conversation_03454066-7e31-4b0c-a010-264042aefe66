// Extract database connection information for VS Code extensions
console.log('🔍 Extracting Supabase connection details for VS Code...\n');

// Your current DATABASE_URL from the server logs
const databaseUrl = '*******************************************************************************************/postgres';

// Parse the connection string
function parseConnectionString(url) {
  const regex = /postgresql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)/;
  const match = url.match(regex);
  
  if (match) {
    return {
      username: match[1],
      password: match[2],
      host: match[3],
      port: match[4],
      database: match[5]
    };
  }
  return null;
}

// For the actual password, you'll need to get it from your environment
console.log('📋 VS Code Database Extension Connection Details:');
console.log('================================================');
console.log('Host: aws-0-us-west-1.pooler.supabase.com');
console.log('Port: 5432');
console.log('Database: postgres');
console.log('Username: drizzle_user.byearryckdwmajygqdpx');
console.log('Password: [You need to get this from your DATABASE_URL environment variable]');
console.log('SSL Mode: Prefer (or Require)');
console.log('');

console.log('🔧 How to get your password:');
console.log('1. Check your .env file (if you have one)');
console.log('2. Or check your environment variables');
console.log('3. Or look in your start-supabase-server.bat file');
console.log('');

console.log('📱 Alternative: Supabase Dashboard');
console.log('You can also access your database through the web dashboard:');
console.log('URL: https://byearryckdwmajygqdpx.supabase.co');
console.log('Go to: Table Editor, SQL Editor, or Database sections');
console.log('');

console.log('🔌 Recommended VS Code Extensions:');
console.log('1. "PostgreSQL" by Chris Kolkman');
console.log('2. "Database Client" by Weijan Chen');
console.log('3. "SQLTools" by Matheus Teixeira + PostgreSQL driver');
