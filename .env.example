# Database Connection (Required)
DATABASE_URL=postgresql://username:password@host:port/database

# OpenAI API Key (Required for LLM-generated sommelier tips)
OPENAI_API_KEY=sk-your-openai-api-key-here

# Supabase Storage (Optional - for media uploads)
# If not configured, media upload features will be disabled
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Server Configuration
PORT=5000
NODE_ENV=development