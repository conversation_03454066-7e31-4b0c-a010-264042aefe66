// Test the wine scores API to check if expectedCharacteristics are included
async function testWineScoresAPI() {
  console.log('Testing wine scores API for expectedCharacteristics...');
  
  // Test with one of the known emails
  const testEmail = '<EMAIL>';
  const apiUrl = `http://localhost:5000/api/dashboard/${testEmail}/scores`;
  
  try {
    console.log(`\nFetching wine scores from: ${apiUrl}`);
    
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    
    console.log('\n=== WINE SCORES API RESPONSE ===');
    console.log(`Total wines: ${data.scores ? data.scores.length : 0}`);
    
    if (data.scores && data.scores.length > 0) {
      console.log('\n--- First 3 wines ---');
      data.scores.slice(0, 3).forEach((wine, index) => {
        console.log(`\n${index + 1}. ${wine.wineName}`);
        console.log(`   Wine ID: ${wine.wineId}`);
        console.log(`   Type: ${wine.wineType || 'N/A'}`);
        console.log(`   Region: ${wine.region || 'N/A'}`);
        console.log(`   Average Score: ${wine.averageScore}`);
        console.log(`   Has expectedCharacteristics: ${wine.expectedCharacteristics ? 'YES' : 'NO'}`);
        
        if (wine.expectedCharacteristics) {
          console.log(`   Expected Characteristics:`, wine.expectedCharacteristics);
        } else {
          console.log(`   Expected Characteristics: null/undefined`);
        }
      });
      
      // Check how many wines have expected characteristics
      const winesWithCharacteristics = data.scores.filter(wine => wine.expectedCharacteristics);
      console.log(`\n📊 Summary:`);
      console.log(`   Total wines: ${data.scores.length}`);
      console.log(`   Wines with expectedCharacteristics: ${winesWithCharacteristics.length}`);
      console.log(`   Percentage: ${((winesWithCharacteristics.length / data.scores.length) * 100).toFixed(1)}%`);
      
    } else {
      console.log('\nNo wine scores found for this user');
    }
    
  } catch (error) {
    console.error('Error testing wine scores API:', error);
  }
}

// Run the test
testWineScoresAPI();
