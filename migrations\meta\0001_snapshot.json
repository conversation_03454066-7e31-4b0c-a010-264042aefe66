{"id": "c0e6c15f-69a6-46c8-9e59-6904a76c383a", "prevId": "1c2927fe-15be-4255-94f4-405a22fae18a", "version": "7", "dialect": "postgresql", "tables": {"public.glossary_terms": {"name": "glossary_terms", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "term": {"name": "term", "type": "text", "primaryKey": false, "notNull": true}, "variations": {"name": "variations", "type": "text[]", "primaryKey": false, "notNull": false}, "definition": {"name": "definition", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_glossary_terms_term": {"name": "idx_glossary_terms_term", "columns": [{"expression": "term", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_glossary_terms_category": {"name": "idx_glossary_terms_category", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"glossary_terms_term_unique": {"name": "glossary_terms_term_unique", "nullsNotDistinct": false, "columns": ["term"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.packages": {"name": "packages", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_packages_code": {"name": "idx_packages_code", "columns": [{"expression": "code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"packages_code_unique": {"name": "packages_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.participants": {"name": "participants", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "session_id": {"name": "session_id", "type": "uuid", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "display_name": {"name": "display_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "is_host": {"name": "is_host", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "progress_ptr": {"name": "progress_ptr", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "last_active": {"name": "last_active", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_participants_session": {"name": "idx_participants_session", "columns": [{"expression": "session_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_participants_email_session": {"name": "idx_participants_email_session", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "session_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"participants_session_id_sessions_id_fk": {"name": "participants_session_id_sessions_id_fk", "tableFrom": "participants", "tableTo": "sessions", "columnsFrom": ["session_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.responses": {"name": "responses", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "participant_id": {"name": "participant_id", "type": "uuid", "primaryKey": false, "notNull": false}, "slide_id": {"name": "slide_id", "type": "uuid", "primaryKey": false, "notNull": false}, "answer_json": {"name": "answer_json", "type": "jsonb", "primaryKey": false, "notNull": true}, "answered_at": {"name": "answered_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "synced": {"name": "synced", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}}, "indexes": {"idx_responses_participant": {"name": "idx_responses_participant", "columns": [{"expression": "participant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_responses_synced": {"name": "idx_responses_synced", "columns": [{"expression": "synced", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"responses_participant_id_participants_id_fk": {"name": "responses_participant_id_participants_id_fk", "tableFrom": "responses", "tableTo": "participants", "columnsFrom": ["participant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "responses_slide_id_slides_id_fk": {"name": "responses_slide_id_slides_id_fk", "tableFrom": "responses", "tableTo": "slides", "columnsFrom": ["slide_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"responses_participant_id_slide_id_unique": {"name": "responses_participant_id_slide_id_unique", "nullsNotDistinct": false, "columns": ["participant_id", "slide_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "package_id": {"name": "package_id", "type": "uuid", "primaryKey": false, "notNull": false}, "short_code": {"name": "short_code", "type": "<PERSON><PERSON><PERSON>(8)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'waiting'"}, "started_at": {"name": "started_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "active_participants": {"name": "active_participants", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_sessions_package_id": {"name": "idx_sessions_package_id", "columns": [{"expression": "package_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_sessions_short_code": {"name": "idx_sessions_short_code", "columns": [{"expression": "short_code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"sessions_package_id_packages_id_fk": {"name": "sessions_package_id_packages_id_fk", "tableFrom": "sessions", "tableTo": "packages", "columnsFrom": ["package_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"sessions_short_code_unique": {"name": "sessions_short_code_unique", "nullsNotDistinct": false, "columns": ["short_code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.slides": {"name": "slides", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "package_id": {"name": "package_id", "type": "uuid", "primaryKey": false, "notNull": false}, "position": {"name": "position", "type": "integer", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "section_type": {"name": "section_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "payload_json": {"name": "payload_json", "type": "jsonb", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_slides_package_position": {"name": "idx_slides_package_position", "columns": [{"expression": "package_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "position", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"slides_package_id_packages_id_fk": {"name": "slides_package_id_packages_id_fk", "tableFrom": "slides", "tableTo": "packages", "columnsFrom": ["package_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}