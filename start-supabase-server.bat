@echo off
echo 🍷 Starting server with Supabase database...

REM Set Supabase environment variables
set DATABASE_URL=postgresql://postgres.byearryckdwmajygqdpx:<EMAIL>:5432/postgres
set SUPABASE_URL=https://byearryckdwmajygqdpx.supabase.co
set SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ5ZWFycnlja2R3bWFqeWdxZHB4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODY4NDMzMSwiZXhwIjoyMDY0MjYwMzMxfQ.20nKBLDl_4zgfr3yyfdvZa9HY3NSBdpbsOzxQXRJuo4

echo ✅ Environment variables set
echo 📊 DATABASE_URL: %DATABASE_URL:~0,50%...
echo 🚀 Starting server on port 3001...

REM Start the server
set PORT=3001
npm run dev
