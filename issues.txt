1. edit wine from som ashboard dropdown does not work. it works when you click on the "edit packages" but when you click on that little notch for it to do a drop down on the som dashboard itself, and it does a drop down of the wines, the edit button does not work
3. can't type on the description slides .... it keeps updating on the slide editor so it slows down hard on the slide editor package editor--> we need to make sure that each slide works perfectly, is perfectly connected to a hooked up to what slides materialize when we create insert, and we make sure and really think through/plan how the package editor is showing and supporting all these different files
4. make the images support more clear, and that it loads the images of the wine on the intro screen for each wine and then the intro image for each package.
5. okay so we need to make it clear what you're editing (inside the slide package editor, in terms of slide and number order). <PERSON> could not really tell what slide he was exactly editing. Its also pretty cluncky and this goes into 6 and 7 as it does not feel or work really fast or good. <PERSON> could NOT EDIT AT ALL as per #3 (well he did-- he got a few words out but it took a super long time).
6. lets just make the slide package editor more clearer/cleaner/optimizeas a result ^ can you please identify and issues, errors, problems, ways that it was implemented poorly etc
7. when you look at a slide (the yes/no one AND text description) there is only a title and a description -- there is no place for someone to type for a text input question.... so we really, again, need to make sure taht everything we connect is solid, precise, well connected, and optimized af. We need to make this clean without breaking functionality.
8. Need arrows or something to make the slides move better (move them around / their order more easily) within the package editor. we need it very clean, clear, easy. very distinct, simple, efecient.
9. Need a more clear/template for a "welcome slide" / it disappears when you're engaged with the rest of the side bar. so basically lets just organize the package editor