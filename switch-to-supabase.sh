#!/bin/bash

echo "🍷 Switching to Supabase database for testing..."

# Backup current DATABASE_URL
export OLD_DATABASE_URL="$DATABASE_URL"

# Set Supabase connection
export DATABASE_URL="postgresql://postgres.byearryckdwmajygqdpx:<EMAIL>:5432/postgres"

# Set Supabase environment variables
export SUPABASE_URL="https://byearryckdwmajygqdpx.supabase.co"
export SUPABASE_SERVICE_ROLE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ5ZWFycnlja2R3bWFqeWdxZHB4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODY4NDMzMSwiZXhwIjoyMDY0MjYwMzMxfQ.20nKBLDl_4zgfr3yyfdvZa9HY3NSBdpbsOzxQXRJuo4"

echo "✅ Switched to Supabase database"
echo "📊 You can now test with real data using these emails:"
echo "   - <EMAIL> (64 responses)"
echo "   - <EMAIL> (65 responses)"
echo "   - <EMAIL> (57 responses)"
echo "   - <EMAIL> (63 responses)"
echo "   - <EMAIL> (53 responses)"
echo "   - <EMAIL> (55 responses)"
echo "   - <EMAIL> (65 responses)"
echo "   - <EMAIL> (59 responses)"
echo "   - <EMAIL> (65 responses)"
echo ""
echo "🚀 Start your server with: PORT=3001 npm run dev"
echo ""
echo "🔄 To switch back to local database, run: source switch-to-local.sh" 